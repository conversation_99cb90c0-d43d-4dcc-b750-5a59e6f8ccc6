import {PayloadAction, createSlice} from '@reduxjs/toolkit';

import {NotificationItem} from '../models/notification';
import {fetchNotifications} from '../actions/notificationAction';

interface notificationSimpleResponse {
  data: Array<NotificationItem>;
  totalCount: number;
  loading: boolean;
}

export type {notificationSimpleResponse};

const initState: notificationSimpleResponse = {
  data: [],
  totalCount: 0,
  loading: false,
};

export const notificationSlice = createSlice({
  name: 'notification',
  initialState: initState,
  reducers: {
    setData: <K extends keyof notificationSimpleResponse>(
      state: notificationSimpleResponse,
      action: PayloadAction<{
        stateName: K;
        data: notificationSimpleResponse[K];
      }>,
    ) => {
      state[action.payload.stateName] = action.payload.data;
    },
  },
  extraReducers: builder => {
    builder.addCase(fetchNotifications.pending, state => {
      state.loading = true;
    });
    builder.addCase(fetchNotifications.fulfilled, (state, action) => {
      state.data = action.payload;
      state.loading = false;
    });
  },
});

export const {setData} = notificationSlice.actions;

export default notificationSlice.reducer;
