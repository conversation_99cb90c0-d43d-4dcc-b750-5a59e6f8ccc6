import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Image,
  Dimensions,
} from 'react-native';
import {AppSvg, FDialog, ListTile} from 'wini-mobile-components';
import {useNavigation} from '@react-navigation/native';
import {navigate, RootScreen} from '../../../router/router';
import iconSvg from '../../../svg/icon';
import CartIcon from '../../../components/CartIcon';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {ColorThemes} from '../../../assets/skin/colors';
import ConfigAPI from '../../../Config/ConfigAPI';
import {dialogCheckAcc} from '../mainLayout';
import {TypoSkin} from '../../../assets/skin/typography';
import FastImage from 'react-native-fast-image';
import HeaderBackground from '../../../modules/shop/component/HeaderShop';
interface HomeHeaderProps {
  onSearchPress?: () => void;
}

export const HomeHeader: React.FC<HomeHeaderProps> = ({onSearchPress}) => {
  const navigation = useNavigation<any>();
  const customer = useSelectorCustomerState().data;
  const dialogRef = React.useRef<any>(null);

  return (
    <View style={styles.header}>
      <FDialog ref={dialogRef} />
      {/* Background màu xanh */}
      <HeaderBackground />

      {/* Nội dung header */}
      <ListTile
        leading={
          customer?.AvatarUrl ? (
            <FastImage
              key={customer?.AvatarUrl}
              source={
                customer?.AvatarUrl
                  ? {
                      uri: customer?.AvatarUrl.includes('https')
                        ? customer?.AvatarUrl
                        : ConfigAPI.urlImg + customer?.AvatarUrl,
                    }
                  : require('../../../assets/images/logo.png')
              }
              style={{
                width: 40,
                height: 40,
                borderRadius: 50,
                backgroundColor: '#f0f0f0',
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
              }}
            />
          ) : (
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 50,
                backgroundColor: ColorThemes.light.primary_main_color,
                alignItems: 'center',
                justifyContent: 'center',
              }}>
              {customer?.Name ? (
                <Text
                  style={{
                    ...TypoSkin.heading7,
                    color: ColorThemes.light.neutral_absolute_background_color,
                  }}>
                  {customer?.Name ? customer?.Name.charAt(0).toUpperCase() : ''}
                </Text>
              ) : (
                <Image
                  source={require('../../../assets/images/logo.png')}
                  style={{width: '100%', height: '100%', borderRadius: 100}}
                />
              )}
            </View>
          )
        }
        title={`Xin chào, ${customer?.Name ?? 'Người dùng'}`}
        style={{
          width: '100%',
          flex: 1,
          padding: 0,
          paddingHorizontal: 16,
          backgroundColor: ColorThemes.light.transparent,
        }}
        trailing={
          <View style={styles.rightIcons}>
            <Image
              source={require('../../../assets/images/logo.png')}
              style={{
                width: 30,
                height: 30,
                borderRadius: 20,
              }}
            />

            {/* User profile icon */}
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigate(RootScreen.Notification);
              }}>
              <View style={styles.iconCircle}>
                <AppSvg SvgSrc={iconSvg.notification} size={16} />
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.iconButton}
              onPress={() => {
                if (!customer?.Id) {
                  dialogCheckAcc(dialogRef);
                  return;
                }
                navigate(RootScreen.CartPage);
              }}>
              <View style={styles.iconCircle}>
                <CartIcon isHome color="#0033CC" size={18} showBadge={true} />
              </View>
            </TouchableOpacity>
          </View>
        }
        bottom={
          <View style={styles.searchContainer}>
            <View
              style={{
                flexDirection: 'row',
                width: Dimensions.get('window').width - 32,
                alignItems: 'center',
                paddingVertical: 4,
              }}>
              <TouchableOpacity
                style={styles.searchBar}
                onPress={onSearchPress}
                activeOpacity={0.8}>
                <Text style={styles.searchPlaceholder}>Bạn muốn tìm gì?</Text>
              </TouchableOpacity>
              {/* <TouchableOpacity style={styles.filterButton}>
                <AppSvg SvgSrc={iconSvg.filter} size={24} />
              </TouchableOpacity> */}
            </View>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    width: '100%',
    height: 150,
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 8,
  },
  iconCircle: {
    width: 32,
    height: 32,
    borderRadius: 20,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    width: '100%',
  },
  searchBar: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 11,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderWidth: 1,
  },
  searchPlaceholder: {
    color: '#999',
    fontSize: 14,
  },
  filterButton: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
